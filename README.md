# Texas Laws Personal Injury Project

This project provides tools for processing, extracting, and analyzing legal citations in Texas laws, particularly focusing on personal injury statutes.

## Repository Structure

The repository has been organized into the following directory structure for better maintainability and clarity:

```
texas-laws-personalinjury/
├── src/                           # Source code
│   ├── extractors/                # Citation extraction modules
│   ├── validators/                # Validation logic
│   ├── database/                  # Database interactions
│   ├── processing/                # Processing pipelines
│   └── utils/                     # Common utilities
├── apps/                          # Applications
│   └── document_explorer/         # Web app for exploring documents 
├── tests/                         # Testing code
├── scripts/                       # One-off scripts
├── analysis/                      # Analysis scripts
├── logs/                          # Log directory
├── data/                          # Data files
└── documentation/                 # Documentation files
```

## Getting Started

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. Process documents:
   ```
   python -m src.processing.batch_processor
   ```

4. Load citations into Neo4j:
   ```
   python -m src.database.neo4j_citation_loader
   ```

## Key Components

- **Citation Extraction**: Hybrid approach using regex patterns and LLM assistance
- **Document Processing**: Batch processing with error handling and state tracking
- **Neo4j Integration**: Graph database for citation relationships
- **Validation**: Verification of citation accuracy and completeness

## Court Listener Integration

### Overview
This project includes a robust multi-system data processing pipeline for integrating Court Listener API data. The pipeline is designed to:

- Fetch case data from Court Listener
- Process and transform case information
- Store data across four different databases:
  1. Supabase: Case metadata and basic information
  2. Neo4j: Case relationships and graph-based insights
  3. Google Cloud Storage: Full-text case documents
  4. Pinecone: Vector embeddings for semantic search

### Key Components

#### Data Processing Pipeline
- `debug_case_processing.py`: End-to-end testing script for case processing
- Connectors for each storage system with enhanced error handling
- Dynamic schema compatibility across storage platforms

#### Storage Systems
- **Supabase**: 
  - Stores case metadata
  - Implements row-level security
  - Uses correct column names (`status`, `updated_at`)

- **Neo4j**: 
  - Creates relationships between cases
  - Enables advanced graph-based queries

- **Google Cloud Storage**: 
  - Stores full-text case documents
  - Generates unique paths and URLs for each document

- **Pinecone**: 
  - Stores vector embeddings of case texts
  - Enables semantic search capabilities

### Workflow
1. Fetch cases from Court Listener
2. Process and transform data
3. Validate and clean case information
4. Store data across multiple databases
5. Prepare for retrieval by separate application

### Performance Considerations
- Batch processing with error recovery
- Minimal real-time API dependencies
- Optimized for fast query responses

### Future Improvements
- Comprehensive testing across jurisdictions
- Advanced AI analysis features
- Scalable deployment infrastructure

### Dependencies
- Python packages for API interactions
- Environment variable management
- Secure API key handling

### Usage
Refer to individual connector scripts in `src/processing/storage/` for detailed implementation.

## Documentation

For detailed documentation, see the `documentation/` directory and READMEs within each module directory.
